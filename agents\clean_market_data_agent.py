#!/usr/bin/env python3
"""
CLEAN MARKET DATA AGENT
Modern implementation with proper async handling and date formats

Features:
- Proper SmartAPI date format handling (YYYY-MM-DD HH:MM)
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Clean async/await patterns
- WebSocket v2 integration
- Comprehensive error handling
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import polars as pl
import random
import os
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest

logger = logging.getLogger(__name__)

class CleanMarketDataAgent(BaseAgent):
    """
    Clean Market Data Agent with modern architecture
    
    Features:
    - Proper date format handling
    - Modern timeframes
    - Clean async patterns
    - WebSocket v2 integration
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanMarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client
        self.smartapi_client = None
        
        # Data storage with training timeframes
        training_timeframes = getattr(config, 'timeframes', ["1min", "3min", "5min", "15min"])
        self.timeframe_data = {tf: {} for tf in training_timeframes}
        
        # Real-time data
        self.realtime_data = {}
        
        # Instrument mapping (simplified)
        self.instrument_map = {}
        
        # Configuration - will be updated dynamically by stock selector
        self.config = config
        self.selected_stocks = []  # Start empty, will be updated by stock selector
        
        # Retry configuration for historical data downloads
        self.retry_config = {
            'max_retries': getattr(config, 'data_download_max_retries', 3),
            'base_delay': getattr(config, 'data_download_base_delay', 2.0),  # seconds
            'max_delay': getattr(config, 'data_download_max_delay', 30.0),   # seconds
            'exponential_base': getattr(config, 'data_download_exponential_base', 2.0),
            'jitter': getattr(config, 'data_download_jitter', True)
        }
        
        self.log_info(f"Initialized for {len(self.selected_stocks)} stocks")
        self.log_info(f"Retry config: max_retries={self.retry_config['max_retries']}, base_delay={self.retry_config['base_delay']}s")
    
    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Clean Market Data Agent...")
            
            # Load credentials
            credentials = self._load_credentials()
            if not credentials:
                self.log_warning("No SmartAPI credentials found, running in demo mode")
                self.initialized = True
                return True
            
            # Initialize SmartAPI client
            self.smartapi_client = ModernSmartAPIClient(credentials)
            
            # Authenticate
            auth_success = await self.smartapi_client.authenticate()
            if not auth_success:
                self.log_error("SmartAPI authentication failed, falling back to demo mode")
                self.smartapi_client = None
            
            # Subscribe to stock universe updates
            self.event_bus.subscribe("STOCK_UNIVERSE_UPDATED", self._handle_universe_update)
            
            # Initialize instrument mapping
            await self._initialize_instruments()
            
            self.initialized = True
            self.log_info("Clean Market Data Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        """Load SmartAPI credentials from environment"""
        try:
            api_key = os.getenv('SMARTAPI_API_KEY')
            username = os.getenv('SMARTAPI_USERNAME')
            password = os.getenv('SMARTAPI_PASSWORD')
            totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
            
            self.log_info(f"Loading credentials - API Key: {'***' if api_key else 'None'}, Username: {'***' if username else 'None'}")
            
            if all([api_key, username, password, totp_token]):
                self.log_info("All SmartAPI credentials found")
                return SmartAPICredentials(
                    api_key=api_key,
                    username=username,
                    password=password,
                    totp_token=totp_token
                )
            else:
                missing = []
                if not api_key: missing.append('SMARTAPI_API_KEY')
                if not username: missing.append('SMARTAPI_USERNAME')
                if not password: missing.append('SMARTAPI_PASSWORD')
                if not totp_token: missing.append('SMARTAPI_TOTP_TOKEN')
                self.log_warning(f"Missing credentials: {', '.join(missing)}")
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
            return None
    
    async def _initialize_instruments(self):
        """Initialize instrument mapping"""
        try:
            # Simplified instrument mapping for demo
            # In production, this would load from SmartAPI instrument master
            base_token = 1000
            for i, symbol in enumerate(self.selected_stocks):
                self.instrument_map[symbol] = {
                    'token': str(base_token + i),
                    'symbol': symbol,
                    'exchange': 'NSE',
                    'lot_size': 1
                }
            
            self.log_info(f"Initialized instruments for {len(self.instrument_map)} symbols")
            
        except Exception as e:
            self.log_error(f"Failed to initialize instruments: {e}")
    
    async def _handle_universe_update(self, event):
        """Handle stock universe update from dynamic selector"""
        try:
            new_universe = event.data.get('universe', [])
            if new_universe:
                old_count = len(self.selected_stocks)
                self.selected_stocks = new_universe
                
                self.log_info(f"Updated stock universe: {old_count} -> {len(self.selected_stocks)} stocks")
                
                # Re-initialize instruments for new stocks
                await self._initialize_instruments()
                
                # If we're already running, download data for new stocks
                if hasattr(self, 'running') and self.running:
                    await self._download_historical_data()
                    
        except Exception as e:
            self.log_error(f"Failed to handle universe update: {e}")
    
    async def start(self):
        """Start the market data agent"""
        try:
            self.log_info("Starting Clean Market Data Agent...")
            
            # Wait for stock universe to be populated
            await self._wait_for_stock_universe()
            
            # Download historical data
            await self._download_historical_data()
            
            # Initialize WebSocket if available
            if self.smartapi_client:
                await self._initialize_websocket()
            
            # Start data processing loop
            await self._start_data_loop()
            
        except Exception as e:
            self.log_error(f"Error starting agent: {e}")
    
    async def _wait_for_stock_universe(self):
        """Wait for stock universe to be populated by dynamic selector"""
        try:
            self.log_info("Waiting for stock universe from dynamic selector...")
            
            # Wait up to 60 seconds for stock universe
            timeout = 60
            elapsed = 0
            
            while elapsed < timeout:
                if self.selected_stocks:
                    self.log_info(f"Received stock universe: {len(self.selected_stocks)} stocks")
                    return
                
                await asyncio.sleep(1)
                elapsed += 1
            
            # Timeout - use fallback stocks from config
            fallback_stocks = getattr(self.config, 'fallback_stocks', [
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"
            ])
            
            self.log_warning(f"Stock universe timeout, using {len(fallback_stocks)} fallback stocks")
            self.selected_stocks = fallback_stocks
            await self._initialize_instruments()
            
        except Exception as e:
            self.log_error(f"Failed to wait for stock universe: {e}")
    
    async def _retry_with_backoff(self, func, *args, **kwargs):
        """
        Retry a function with exponential backoff and jitter
        
        Args:
            func: The async function to retry
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function call
            
        Raises:
            The last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.retry_config['max_retries'] + 1):  # +1 for initial attempt
            try:
                if attempt > 0:
                    # Calculate delay with exponential backoff
                    delay = min(
                        self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** (attempt - 1)),
                        self.retry_config['max_delay']
                    )
                    
                    # Add jitter to prevent thundering herd
                    if self.retry_config['jitter']:
                        delay = delay * (0.5 + random.random() * 0.5)  # 50-100% of calculated delay
                    
                    self.log_info(f"Retrying in {delay:.2f} seconds (attempt {attempt + 1}/{self.retry_config['max_retries'] + 1})")
                    await asyncio.sleep(delay)
                
                # Attempt the function call
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    self.log_info(f"Retry successful after {attempt} attempts")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.retry_config['max_retries']:
                    self.log_warning(f"Attempt {attempt + 1} failed: {e}")
                else:
                    self.log_error(f"All {self.retry_config['max_retries'] + 1} attempts failed. Last error: {e}")
        
        # If we get here, all retries failed
        raise last_exception
    
    async def _download_historical_data(self):
        """Download historical data with proper date handling and retry mechanism"""
        try:
            self.log_info(f"Downloading historical data for {len(self.selected_stocks)} stocks...")
            
            # Calculate date range (25 days back)
            end_date = datetime.now().replace(hour=15, minute=30, second=0, microsecond=0)
            start_date = end_date - timedelta(days=25)
            
            # Ensure we start from market hours
            start_date = start_date.replace(hour=9, minute=15, second=0, microsecond=0)
            
            successful_downloads = 0
            failed_downloads = 0
            
            for i, symbol in enumerate(self.selected_stocks):
                try:
                    self.log_info(f"Processing {symbol} ({i+1}/{len(self.selected_stocks)})")
                    
                    if self.smartapi_client:
                        # Use retry mechanism for real data download
                        await self._retry_with_backoff(
                            self._download_real_data_with_validation, 
                            symbol, start_date, end_date
                        )
                    else:
                        # Demo data doesn't need retry mechanism
                        await self._create_demo_data(symbol, start_date, end_date)
                    
                    # Generate higher timeframes
                    await self._generate_higher_timeframes(symbol)
                    
                    successful_downloads += 1
                    self.log_info(f"✓ Successfully downloaded data for {symbol}")
                    
                    # Rate limiting - 1 second sleep after every historical data download to avoid timeouts
                    await asyncio.sleep(1.0)
                    
                except Exception as e:
                    failed_downloads += 1
                    self.log_error(f"✗ Failed to download data for {symbol} after all retries: {e}")
                    
                    # Continue with next symbol instead of stopping the entire process
                    continue
            
            # Summary statistics
            total_stocks = len(self.selected_stocks)
            success_rate = (successful_downloads / total_stocks * 100) if total_stocks > 0 else 0
            
            self.log_info(f"Historical data download completed:")
            self.log_info(f"  ✓ Successful: {successful_downloads}/{total_stocks} ({success_rate:.1f}%)")
            self.log_info(f"  ✗ Failed: {failed_downloads}/{total_stocks}")
            
            if failed_downloads > 0:
                self.log_warning(f"Some downloads failed. System will continue with available data.")
            
        except Exception as e:
            self.log_error(f"Failed to download historical data: {e}")
    
    async def _download_real_data_with_validation(self, symbol: str, start_date: datetime, end_date: datetime):
        """Download real data from SmartAPI with validation (used with retry mechanism)"""
        instrument = self.instrument_map.get(symbol)
        if not instrument:
            raise ValueError(f"No instrument found for {symbol}")
        
        # Get historical data in batches
        data = await self.smartapi_client.get_historical_data_batch(
            symbol_token=instrument['token'],
            exchange=instrument['exchange'],
            start_date=start_date,
            end_date=end_date,
            interval="ONE_MINUTE"
        )
        
        if not data:
            raise ValueError(f"No data received for {symbol}")
        
        # Convert to DataFrame with proper date parsing
        df_data = []
        parse_errors = 0
        
        for candle in data:
            try:
                # Parse timestamp - SmartAPI returns in format: "2025-01-31 09:15:00"
                timestamp_str = candle[0]
                timestamp = self._parse_smartapi_timestamp(timestamp_str)
                
                df_data.append({
                    'timestamp': timestamp,
                    'open': float(candle[1]),
                    'high': float(candle[2]),
                    'low': float(candle[3]),
                    'close': float(candle[4]),
                    'volume': int(candle[5])
                })
            except Exception as e:
                parse_errors += 1
                self.log_warning(f"Failed to parse candle data for {symbol}: {e}")
                continue
        
        if not df_data:
            raise ValueError(f"No valid candle data parsed for {symbol} (parse errors: {parse_errors})")
        
        # Validate data quality
        if len(df_data) < 10:  # Minimum threshold for meaningful data
            raise ValueError(f"Insufficient data for {symbol}: only {len(df_data)} candles")
        
        # Store the data
        self.timeframe_data["1min"][symbol] = pl.DataFrame(df_data)
        
        # Log success with details
        if parse_errors > 0:
            self.log_info(f"Stored {len(df_data)} 1-min candles for {symbol} ({parse_errors} parse errors)")
        else:
            self.log_info(f"Stored {len(df_data)} 1-min candles for {symbol}")
        
        return len(df_data)  # Return number of records for validation
    
    async def _download_real_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Download real data from SmartAPI (legacy method without retry)"""
        try:
            await self._download_real_data_with_validation(symbol, start_date, end_date)
        except Exception as e:
            self.log_error(f"Failed to download real data for {symbol}: {e}")
            raise
    
    def _parse_smartapi_timestamp(self, timestamp_str: str) -> datetime:
        """Parse SmartAPI timestamp with multiple format support"""
        try:
            # SmartAPI typically returns timestamps in these formats:
            formats = [
                "%Y-%m-%d %H:%M:%S",  # 2025-01-31 09:15:00
                "%d-%m-%Y %H:%M:%S",  # 31-01-2025 09:15:00 (sometimes)
                "%Y-%m-%dT%H:%M:%S",  # 2025-01-31T09:15:00
                "%Y-%m-%d %H:%M",     # 2025-01-31 09:15
            ]
            
            # Clean the timestamp string
            clean_timestamp = timestamp_str.strip()
            if '+' in clean_timestamp:
                clean_timestamp = clean_timestamp.split('+')[0]
            
            for fmt in formats:
                try:
                    return datetime.strptime(clean_timestamp, fmt)
                except ValueError:
                    continue
            
            # If all formats fail, try ISO format
            return datetime.fromisoformat(clean_timestamp.replace('T', ' '))
            
        except Exception as e:
            self.log_error(f"Failed to parse timestamp '{timestamp_str}': {e}")
            # Return current time as fallback
            return datetime.now()
    
    async def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Create realistic demo data"""
        try:
            self.log_info(f"Creating demo data for {symbol}")
            
            # Generate realistic 1-minute data
            current_time = start_date
            demo_data = []
            base_price = random.uniform(100, 2000)  # Random base price
            
            while current_time < end_date:
                # Skip weekends
                if current_time.weekday() < 5:  # Monday = 0, Friday = 4
                    # Market hours: 9:15 AM to 3:30 PM
                    if (9 <= current_time.hour < 15) or (current_time.hour == 15 and current_time.minute <= 30):
                        if current_time.hour >= 9 and current_time.minute >= 15:
                            # Generate realistic price movement
                            price_change = random.uniform(-0.015, 0.015)  # ±1.5% change
                            base_price *= (1 + price_change)
                            
                            # Ensure positive price
                            base_price = max(base_price, 10)
                            
                            high = base_price * random.uniform(1.0, 1.008)
                            low = base_price * random.uniform(0.992, 1.0)
                            volume = random.randint(1000, 50000)
                            
                            demo_data.append({
                                'timestamp': current_time,
                                'open': round(base_price, 2),
                                'high': round(high, 2),
                                'low': round(low, 2),
                                'close': round(base_price, 2),
                                'volume': volume
                            })
                
                current_time += timedelta(minutes=1)
            
            # Store demo data
            if demo_data:
                self.timeframe_data["1min"][symbol] = pl.DataFrame(demo_data)
                self.log_info(f"Created {len(demo_data)} demo 1-min candles for {symbol}")
            
        except Exception as e:
            self.log_error(f"Failed to create demo data for {symbol}: {e}")
    
    async def _generate_higher_timeframes(self, symbol: str):
        """Generate higher timeframes from 1-minute data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            df_1min = self.timeframe_data["1min"][symbol]
            if len(df_1min) == 0:
                return
            
            # Training timeframe configurations
            timeframe_configs = {}
            for tf in self.timeframe_data.keys():
                if tf != "1min":  # Skip 1min as it's the base
                    if tf.endswith("min"):
                        minutes = int(tf.replace("min", ""))
                        timeframe_configs[tf] = f"{minutes}m"
                    elif tf.endswith("hr"):
                        hours = int(tf.replace("hr", ""))
                        timeframe_configs[tf] = f"{hours}h"
            
            for tf_name, tf_period in timeframe_configs.items():
                try:
                    # Use group_by_dynamic for time-based aggregation
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=tf_period,
                        closed="left"
                    ).agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume")
                    ]).filter(
                        pl.col("open").is_not_null()
                    )
                    
                    if len(df_tf) > 0:
                        self.timeframe_data[tf_name][symbol] = df_tf
                        self.log_debug(f"Generated {len(df_tf)} {tf_name} candles for {symbol}")
                    
                except Exception as e:
                    self.log_warning(f"Failed to generate {tf_name} for {symbol}: {e}")
            
        except Exception as e:
            self.log_error(f"Failed to generate higher timeframes for {symbol}: {e}")
    
    async def _initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.log_info("Initializing WebSocket connection...")
            
            # Set up WebSocket callbacks
            callbacks = {
                'on_connect': self._on_websocket_connect,
                'on_data': self._on_websocket_data,
                'on_error': self._on_websocket_error,
                'on_close': self._on_websocket_close
            }
            
            # Initialize WebSocket
            success = await self.smartapi_client.initialize_websocket(callbacks)
            
            if success:
                # Subscribe to symbols
                await self._subscribe_to_symbols()
                self.log_info("WebSocket initialized and subscribed successfully")
            else:
                self.log_error("WebSocket initialization failed")
            
        except Exception as e:
            self.log_error(f"Failed to initialize WebSocket: {e}")
    
    async def _subscribe_to_symbols(self):
        """Subscribe to symbols for real-time data"""
        try:
            # Prepare subscription data
            token_list = []
            
            for symbol in self.selected_stocks:
                instrument = self.instrument_map.get(symbol)
                if instrument:
                    token_list.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [instrument['token']]
                    })
            
            if token_list:
                # Subscribe to LTP mode
                success = self.smartapi_client.subscribe_symbols(token_list, mode=1)
                if success:
                    self.log_info(f"Subscribed to {len(token_list)} symbols")
                else:
                    self.log_error("Failed to subscribe to symbols")
            
        except Exception as e:
            self.log_error(f"Failed to subscribe to symbols: {e}")
    
    def _on_websocket_connect(self, ws):
        """WebSocket connect callback"""
        self.log_info("WebSocket connected")
        
        # Note: Cannot publish async events from sync callback
        # WebSocket connection status is tracked in smartapi_client
    
    def _on_websocket_data(self, ws, data):
        """WebSocket data callback"""
        try:
            # Process real-time data
            if isinstance(data, dict):
                symbol_token = data.get('tk')
                ltp = data.get('lp')
                
                if symbol_token and ltp:
                    # Find symbol by token
                    symbol = self._get_symbol_by_token(symbol_token)
                    if symbol:
                        # Store real-time data
                        self.realtime_data[symbol] = {
                            'symbol': symbol,
                            'ltp': float(ltp),
                            'timestamp': datetime.now(),
                            'volume': data.get('v', 0),
                            'open': data.get('o', ltp),
                            'high': data.get('h', ltp),
                            'low': data.get('l', ltp)
                        }
                        
                        # Note: Cannot publish async events from sync callback
                        # Real-time data is stored and can be accessed by other components
                        
                        self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"WebSocket data processing error: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error callback"""
        self.log_error(f"WebSocket error: {error}")
        
        # Note: Cannot publish async events from sync callback
        # Error is logged and tracked in smartapi_client
    
    def _on_websocket_close(self, ws, code, reason):
        """WebSocket close callback"""
        self.log_info(f"WebSocket closed: {code} - {reason}")
        
        # Note: Cannot publish async events from sync callback
        # Close status is tracked in smartapi_client
    
    def _get_symbol_by_token(self, token: str) -> Optional[str]:
        """Get symbol by token"""
        for symbol, instrument in self.instrument_map.items():
            if instrument['token'] == token:
                return symbol
        return None
    
    async def _handle_data_request(self, event):
        """Handle data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1min')
            
            if symbol and symbol in self.timeframe_data.get(timeframe, {}):
                data = self.timeframe_data[timeframe][symbol]
                
                # Publish historical data loaded event
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'records': len(data) if data is not None else 0,
                        'data': data  # Pass the DataFrame directly
                    },
                    source=self.name
                )
                
                self.log_info(f"Provided data for {symbol} ({timeframe})")
            else:
                self.log_warning(f"No data available for {symbol} ({timeframe})")
                
        except Exception as e:
            self.log_error(f"Failed to handle data request: {e}")
    
    async def _start_data_loop(self):
        """Start the main data processing loop"""
        try:
            self.log_info("Starting data processing loop...")
            
            while self.running:
                try:
                    # Process any pending updates
                    await self._process_data_updates()
                    
                    # Health check
                    if self.smartapi_client and not self.smartapi_client.websocket_connected:
                        self.log_warning("WebSocket disconnected, attempting reconnection...")
                        # In a real implementation, add reconnection logic here
                    
                    # Sleep for a short interval
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.log_error(f"Error in data processing loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Data processing loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start data processing loop: {e}")
    
    async def _process_data_updates(self):
        """Process any pending data updates"""
        try:
            # Update 1-minute candles with real-time data
            for symbol, rt_data in self.realtime_data.items():
                await self._update_realtime_candle(symbol, rt_data)
            
        except Exception as e:
            self.log_error(f"Failed to process data updates: {e}")
    
    async def _update_realtime_candle(self, symbol: str, rt_data: Dict[str, Any]):
        """Update real-time 1-minute candle data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            # Get current 1-minute candle timestamp
            current_minute = rt_data['timestamp'].replace(second=0, microsecond=0)
            
            # This is a simplified update - in production, you'd want more sophisticated logic
            # to properly update the current candle with real-time data
            
        except Exception as e:
            self.log_error(f"Failed to update realtime candle for {symbol}: {e}")
    
    async def get_historical_data(self, symbol: str, timeframe: str = "1min") -> Optional[pl.DataFrame]:
        """Get historical data for a symbol and timeframe"""
        try:
            if timeframe in self.timeframe_data and symbol in self.timeframe_data[timeframe]:
                return self.timeframe_data[timeframe][symbol]
            return None
            
        except Exception as e:
            self.log_error(f"Failed to get historical data for {symbol}: {e}")
            return None
    
    async def get_realtime_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time data for a symbol"""
        try:
            return self.realtime_data.get(symbol)
            
        except Exception as e:
            self.log_error(f"Failed to get realtime data for {symbol}: {e}")
            return None
    
    async def stop(self):
        """Stop the market data agent"""
        try:
            self.log_info("Stopping Clean Market Data Agent...")
            
            self.running = False
            
            # Disconnect WebSocket
            if self.smartapi_client:
                await self.smartapi_client.disconnect_websocket()
            
            self.log_info("Clean Market Data Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")
    
    async def _perform_additional_health_checks(self) -> Dict[str, Any]:
        """Perform additional health checks"""
        try:
            checks = {}
            
            # Check data availability
            checks['data_available'] = len(self.timeframe_data["1min"]) > 0
            
            # Check SmartAPI client health
            if self.smartapi_client:
                checks['smartapi_healthy'] = self.smartapi_client.is_healthy()
                checks['websocket_connected'] = self.smartapi_client.websocket_connected
            else:
                checks['smartapi_healthy'] = False
                checks['websocket_connected'] = False
            
            # Check real-time data freshness
            if self.realtime_data:
                latest_data_time = max(data['timestamp'] for data in self.realtime_data.values())
                time_since_update = (datetime.now() - latest_data_time).total_seconds()
                checks['realtime_data_fresh'] = time_since_update < 300  # 5 minutes
            else:
                checks['realtime_data_fresh'] = False
            
            return checks
            
        except Exception as e:
            self.log_error(f"Health check failed: {e}")
            return {'error': str(e)}