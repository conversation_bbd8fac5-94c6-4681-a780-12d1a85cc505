#!/usr/bin/env python3
"""
DYNAMIC STOCK SELECTOR AGENT
ML-based dynamic stock universe selection using multiple factors

Features:
- Liquidity-based filtering (volume, turnover)
- Market cap and volatility analysis
- Technical momentum indicators
- ML-based scoring and ranking
- Real-time universe updates
- Risk-adjusted selection criteria

Based on modern best practices:
- Dynamic factor allocation
- Multi-factor quantitative models
- Regime-switching signals
- Cross-sectional optimization
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import polars as pl
from dataclasses import dataclass
import os
from pathlib import Path

# ML imports
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """Stock scoring data structure"""
    symbol: str
    score: float
    liquidity_score: float
    momentum_score: float
    volatility_score: float
    market_cap_score: float
    prediction_confidence: float
    rank: int
    metadata: Dict[str, Any]

@dataclass
class UniverseConfig:
    """Universe selection configuration"""
    min_market_cap: float = 1000_000_000  # 1B INR minimum
    min_avg_volume: int = 100_000  # Minimum daily volume
    min_price: float = 10.0  # Minimum stock price
    max_price: float = 10000.0  # Maximum stock price
    max_volatility: float = 0.05  # Maximum daily volatility (5%)
    min_liquidity_ratio: float = 0.01  # Minimum liquidity ratio
    max_stocks: int = 50  # Maximum stocks in universe
    rebalance_frequency_hours: int = 24  # Rebalance every 24 hours
    
class DynamicStockSelector(BaseAgent):
    """
    Dynamic Stock Selector Agent
    
    Features:
    - ML-based stock scoring
    - Multi-factor analysis
    - Real-time universe updates
    - Risk-adjusted selection
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("DynamicStockSelector", event_bus, config, session_id)
        
        # Configuration
        self.universe_config = UniverseConfig()
        
        # Current universe
        self.current_universe: List[str] = []
        self.stock_scores: Dict[str, StockScore] = {}
        self.last_rebalance: Optional[datetime] = None
        
        # Market data cache
        self.market_data: Dict[str, pl.DataFrame] = {}
        self.fundamental_data: Dict[str, Dict] = {}
        
        # ML Model
        self.scoring_model = None
        self.scaler = StandardScaler() if ML_AVAILABLE else None
        self.model_features = [
            'volume_ratio', 'price_momentum', 'volatility', 'rsi',
            'market_cap_log', 'turnover_ratio', 'price_change_5d',
            'volume_trend', 'liquidity_score'
        ]
        
        # NSE 500 universe (fallback)
        self.nse_500_symbols = self._load_nse_500_symbols()
        
        # Statistics
        self.selection_stats = {
            'total_evaluated': 0,
            'selected_count': 0,
            'last_selection_time': None,
            'selection_duration': 0,
            'model_accuracy': 0.0
        }
        
        self.log_info("Dynamic Stock Selector initialized")
    
    async def initialize(self) -> bool:
        """Initialize the stock selector"""
        try:
            self.log_info("Initializing Dynamic Stock Selector...")
            
            # Load or train ML model
            await self._initialize_ml_model()
            
            # Subscribe to market data events
            self.event_bus.subscribe(EventTypes.MARKET_DATA_RECEIVED, self._handle_market_data)
            self.event_bus.subscribe("REQUEST_STOCK_UNIVERSE", self._handle_universe_request)
            
            # Initial universe selection
            await self._select_initial_universe()
            
            self.initialized = True
            self.log_info("Dynamic Stock Selector initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    async def start(self):
        """Start the stock selector"""
        try:
            self.log_info("Starting Dynamic Stock Selector...")
            
            # Start universe monitoring loop
            await self._start_universe_monitoring()
            
        except Exception as e:
            self.log_error(f"Error starting selector: {e}")
    
    async def _start_universe_monitoring(self):
        """Start the universe monitoring and rebalancing loop"""
        try:
            self.log_info("Starting universe monitoring loop...")
            
            while self.running:
                try:
                    # Check if rebalancing is needed
                    if self._should_rebalance():
                        await self._rebalance_universe()
                    
                    # Update stock scores
                    await self._update_stock_scores()
                    
                    # Sleep for monitoring interval
                    await asyncio.sleep(3600)  # Check every hour
                    
                except Exception as e:
                    self.log_error(f"Error in monitoring loop: {e}")
                    await asyncio.sleep(60)
            
            self.log_info("Universe monitoring loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start universe monitoring: {e}")
    
    def _should_rebalance(self) -> bool:
        """Check if universe rebalancing is needed"""
        if not self.last_rebalance:
            return True
        
        time_since_rebalance = datetime.now() - self.last_rebalance
        return time_since_rebalance.total_seconds() > (self.universe_config.rebalance_frequency_hours * 3600)
    
    async def _select_initial_universe(self):
        """Select initial stock universe"""
        try:
            self.log_info("Selecting initial stock universe...")
            
            # Use NSE 500 as candidate pool
            candidates = self.nse_500_symbols[:100]  # Start with top 100
            
            # Apply basic filters
            filtered_candidates = await self._apply_basic_filters(candidates)
            
            # Score and rank stocks
            scored_stocks = await self._score_stocks(filtered_candidates)
            
            # Select top stocks
            self.current_universe = [stock.symbol for stock in scored_stocks[:self.universe_config.max_stocks]]
            self.stock_scores = {stock.symbol: stock for stock in scored_stocks}
            
            self.last_rebalance = datetime.now()
            
            # Publish universe update
            await self._publish_universe_update()
            
            self.log_info(f"Initial universe selected: {len(self.current_universe)} stocks")
            
        except Exception as e:
            self.log_error(f"Failed to select initial universe: {e}")
            # Fallback to hardcoded list
            self.current_universe = [
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK",
                "HINDUNILVR", "ITC", "SBIN", "BHARTIARTL", "KOTAKBANK"
            ]
    
    async def _rebalance_universe(self):
        """Rebalance the stock universe"""
        try:
            start_time = datetime.now()
            self.log_info("Rebalancing stock universe...")
            
            # Get expanded candidate pool
            candidates = self.nse_500_symbols[:200]  # Expand to top 200
            
            # Apply filters
            filtered_candidates = await self._apply_basic_filters(candidates)
            
            # Score stocks
            scored_stocks = await self._score_stocks(filtered_candidates)
            
            # Select new universe
            new_universe = [stock.symbol for stock in scored_stocks[:self.universe_config.max_stocks]]
            
            # Calculate changes
            added_stocks = set(new_universe) - set(self.current_universe)
            removed_stocks = set(self.current_universe) - set(new_universe)
            
            # Update universe
            self.current_universe = new_universe
            self.stock_scores = {stock.symbol: stock for stock in scored_stocks}
            self.last_rebalance = datetime.now()
            
            # Update statistics
            duration = (datetime.now() - start_time).total_seconds()
            self.selection_stats.update({
                'total_evaluated': len(candidates),
                'selected_count': len(new_universe),
                'last_selection_time': self.last_rebalance,
                'selection_duration': duration
            })
            
            # Publish universe update
            await self._publish_universe_update()
            
            self.log_info(f"Universe rebalanced: +{len(added_stocks)} -{len(removed_stocks)} stocks (duration: {duration:.2f}s)")
            
        except Exception as e:
            self.log_error(f"Failed to rebalance universe: {e}")
    
    async def _apply_basic_filters(self, candidates: List[str]) -> List[str]:
        """Apply basic filtering criteria"""
        try:
            filtered = []
            
            for symbol in candidates:
                try:
                    # Get basic market data (mock for now)
                    market_data = await self._get_market_data(symbol)
                    
                    if market_data is None:
                        continue
                    
                    # Apply filters
                    if (market_data.get('avg_volume', 0) >= self.universe_config.min_avg_volume and
                        self.universe_config.min_price <= market_data.get('price', 0) <= self.universe_config.max_price and
                        market_data.get('volatility', 1.0) <= self.universe_config.max_volatility and
                        market_data.get('market_cap', 0) >= self.universe_config.min_market_cap):
                        
                        filtered.append(symbol)
                
                except Exception as e:
                    self.log_debug(f"Error filtering {symbol}: {e}")
                    continue
            
            self.log_info(f"Filtered {len(candidates)} candidates to {len(filtered)} stocks")
            return filtered
            
        except Exception as e:
            self.log_error(f"Failed to apply basic filters: {e}")
            return candidates[:20]  # Fallback
    
    async def _score_stocks(self, candidates: List[str]) -> List[StockScore]:
        """Score and rank stocks using ML model"""
        try:
            scored_stocks = []
            
            for symbol in candidates:
                try:
                    score = await self._calculate_stock_score(symbol)
                    if score:
                        scored_stocks.append(score)
                        
                except Exception as e:
                    self.log_debug(f"Error scoring {symbol}: {e}")
                    continue
            
            # Sort by score (descending)
            scored_stocks.sort(key=lambda x: x.score, reverse=True)
            
            # Assign ranks
            for i, stock in enumerate(scored_stocks):
                stock.rank = i + 1
            
            self.log_info(f"Scored {len(scored_stocks)} stocks")
            return scored_stocks
            
        except Exception as e:
            self.log_error(f"Failed to score stocks: {e}")
            return []
    
    async def _calculate_stock_score(self, symbol: str) -> Optional[StockScore]:
        """Calculate comprehensive score for a stock"""
        try:
            # Get market data
            market_data = await self._get_market_data(symbol)
            if not market_data:
                return None
            
            # Calculate individual scores
            liquidity_score = self._calculate_liquidity_score(market_data)
            momentum_score = self._calculate_momentum_score(market_data)
            volatility_score = self._calculate_volatility_score(market_data)
            market_cap_score = self._calculate_market_cap_score(market_data)
            
            # Use ML model if available
            if self.scoring_model and ML_AVAILABLE:
                features = self._extract_features(market_data)
                if features is not None:
                    ml_score = self.scoring_model.predict([features])[0]
                    confidence = 0.8  # Mock confidence
                else:
                    ml_score = 0.5
                    confidence = 0.3
            else:
                # Fallback to weighted combination
                ml_score = (
                    liquidity_score * 0.3 +
                    momentum_score * 0.25 +
                    volatility_score * 0.2 +
                    market_cap_score * 0.25
                )
                confidence = 0.6
            
            return StockScore(
                symbol=symbol,
                score=ml_score,
                liquidity_score=liquidity_score,
                momentum_score=momentum_score,
                volatility_score=volatility_score,
                market_cap_score=market_cap_score,
                prediction_confidence=confidence,
                rank=0,  # Will be set later
                metadata={
                    'price': market_data.get('price', 0),
                    'volume': market_data.get('avg_volume', 0),
                    'market_cap': market_data.get('market_cap', 0),
                    'volatility': market_data.get('volatility', 0)
                }
            )
            
        except Exception as e:
            self.log_error(f"Failed to calculate score for {symbol}: {e}")
            return None
    
    def _calculate_liquidity_score(self, market_data: Dict) -> float:
        """Calculate liquidity score (0-1)"""
        try:
            volume = market_data.get('avg_volume', 0)
            turnover = market_data.get('turnover_ratio', 0)
            
            # Normalize volume (log scale)
            volume_score = min(np.log10(max(volume, 1)) / 7, 1.0)  # Max at 10M
            turnover_score = min(turnover / 0.1, 1.0)  # Max at 10%
            
            return (volume_score * 0.7 + turnover_score * 0.3)
            
        except Exception:
            return 0.5
    
    def _calculate_momentum_score(self, market_data: Dict) -> float:
        """Calculate momentum score (0-1)"""
        try:
            price_change_5d = market_data.get('price_change_5d', 0)
            rsi = market_data.get('rsi', 50)
            
            # Momentum score based on price change and RSI
            momentum = (price_change_5d + 0.1) / 0.2  # Normalize around ±10%
            rsi_momentum = (rsi - 30) / 40  # RSI between 30-70 is good
            
            return np.clip((momentum * 0.6 + rsi_momentum * 0.4), 0, 1)
            
        except Exception:
            return 0.5
    
    def _calculate_volatility_score(self, market_data: Dict) -> float:
        """Calculate volatility score (0-1, lower volatility = higher score)"""
        try:
            volatility = market_data.get('volatility', 0.05)
            
            # Inverse volatility score (lower vol = higher score)
            return max(0, 1 - (volatility / self.universe_config.max_volatility))
            
        except Exception:
            return 0.5
    
    def _calculate_market_cap_score(self, market_data: Dict) -> float:
        """Calculate market cap score (0-1)"""
        try:
            market_cap = market_data.get('market_cap', 0)
            
            # Log scale normalization
            if market_cap <= 0:
                return 0
            
            log_cap = np.log10(market_cap)
            # Score between 1B (9) and 1T (12)
            return np.clip((log_cap - 9) / 3, 0, 1)
            
        except Exception:
            return 0.5
    
    def _extract_features(self, market_data: Dict) -> Optional[np.ndarray]:
        """Extract features for ML model"""
        try:
            features = []
            
            for feature in self.model_features:
                if feature == 'volume_ratio':
                    features.append(market_data.get('avg_volume', 0) / 1000000)
                elif feature == 'price_momentum':
                    features.append(market_data.get('price_change_5d', 0))
                elif feature == 'volatility':
                    features.append(market_data.get('volatility', 0))
                elif feature == 'rsi':
                    features.append(market_data.get('rsi', 50) / 100)
                elif feature == 'market_cap_log':
                    cap = market_data.get('market_cap', 1)
                    features.append(np.log10(max(cap, 1)))
                elif feature == 'turnover_ratio':
                    features.append(market_data.get('turnover_ratio', 0))
                elif feature == 'price_change_5d':
                    features.append(market_data.get('price_change_5d', 0))
                elif feature == 'volume_trend':
                    features.append(market_data.get('volume_trend', 0))
                elif feature == 'liquidity_score':
                    features.append(self._calculate_liquidity_score(market_data))
                else:
                    features.append(0.0)
            
            return np.array(features)
            
        except Exception as e:
            self.log_error(f"Failed to extract features: {e}")
            return None
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict]:
        """Get market data for a symbol (mock implementation)"""
        try:
            # In production, this would fetch real market data
            # For now, return mock data with realistic values
            
            import random
            
            base_price = random.uniform(100, 2000)
            volume = random.randint(50000, 5000000)
            market_cap = random.uniform(1e9, 1e12)  # 1B to 1T
            
            return {
                'price': base_price,
                'avg_volume': volume,
                'market_cap': market_cap,
                'volatility': random.uniform(0.01, 0.04),
                'price_change_5d': random.uniform(-0.1, 0.1),
                'rsi': random.uniform(30, 70),
                'turnover_ratio': random.uniform(0.01, 0.05),
                'volume_trend': random.uniform(-0.2, 0.2),
                'liquidity_ratio': volume * base_price / market_cap
            }
            
        except Exception as e:
            self.log_error(f"Failed to get market data for {symbol}: {e}")
            return None
    
    async def _initialize_ml_model(self):
        """Initialize or load ML model"""
        try:
            if not ML_AVAILABLE:
                self.log_warning("ML libraries not available, using rule-based scoring")
                return
            
            model_path = Path("models/stock_selector_model.joblib")
            
            if model_path.exists():
                # Load existing model
                self.scoring_model = joblib.load(model_path)
                self.log_info("Loaded existing ML model")
            else:
                # Train new model
                await self._train_ml_model()
            
        except Exception as e:
            self.log_error(f"Failed to initialize ML model: {e}")
    
    async def _train_ml_model(self):
        """Train ML model for stock scoring"""
        try:
            if not ML_AVAILABLE:
                return
            
            self.log_info("Training ML model for stock selection...")
            
            # Generate synthetic training data (in production, use historical performance)
            X, y = self._generate_training_data()
            
            if len(X) < 100:
                self.log_warning("Insufficient training data, using rule-based scoring")
                return
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            self.scoring_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            self.scoring_model.fit(X_train_scaled, y_train)
            
            # Evaluate
            train_score = self.scoring_model.score(X_train_scaled, y_train)
            test_score = self.scoring_model.score(X_test_scaled, y_test)
            
            self.selection_stats['model_accuracy'] = test_score
            
            # Save model
            os.makedirs("models", exist_ok=True)
            joblib.dump(self.scoring_model, "models/stock_selector_model.joblib")
            joblib.dump(self.scaler, "models/stock_selector_scaler.joblib")
            
            self.log_info(f"ML model trained - Train: {train_score:.3f}, Test: {test_score:.3f}")
            
        except Exception as e:
            self.log_error(f"Failed to train ML model: {e}")
    
    def _generate_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """Generate synthetic training data"""
        try:
            # In production, this would use historical performance data
            n_samples = 1000
            X = []
            y = []
            
            for _ in range(n_samples):
                # Generate random features
                features = np.random.random(len(self.model_features))
                
                # Generate target based on feature combination (synthetic)
                target = (
                    features[0] * 0.2 +  # volume_ratio
                    features[1] * 0.15 + # price_momentum
                    (1 - features[2]) * 0.2 + # volatility (inverse)
                    features[3] * 0.1 +  # rsi
                    features[4] * 0.15 + # market_cap_log
                    features[5] * 0.1 +  # turnover_ratio
                    features[6] * 0.05 + # price_change_5d
                    features[7] * 0.05   # volume_trend
                )
                
                X.append(features)
                y.append(target)
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            self.log_error(f"Failed to generate training data: {e}")
            return np.array([]), np.array([])
    
    def _load_nse_500_symbols(self) -> List[str]:
        """Load NSE 500 symbols"""
        try:
            # In production, load from NSE data or API
            # For now, return a comprehensive list
            return [
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK",
                "HINDUNILVR", "ITC", "SBIN", "BHARTIARTL", "KOTAKBANK",
                "LT", "ASIANPAINT", "MARUTI", "HCLTECH", "AXISBANK",
                "BAJFINANCE", "WIPRO", "NESTLEIND", "ULTRACEMCO", "TITAN",
                "SUNPHARMA", "ONGC", "NTPC", "POWERGRID", "TECHM",
                "TATAMOTORS", "COALINDIA", "BAJAJFINSV", "HDFCLIFE", "GRASIM",
                "ADANIPORTS", "JSWSTEEL", "INDUSINDBK", "TATASTEEL", "CIPLA",
                "DRREDDY", "EICHERMOT", "BRITANNIA", "BPCL", "DIVISLAB",
                "APOLLOHOSP", "HEROMOTOCO", "BAJAJ-AUTO", "SHREECEM", "PIDILITIND",
                "GODREJCP", "DABUR", "MARICO", "COLPAL", "BERGEPAINT"
            ]
            
        except Exception as e:
            self.log_error(f"Failed to load NSE 500 symbols: {e}")
            return ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
    
    async def _update_stock_scores(self):
        """Update scores for current universe"""
        try:
            for symbol in self.current_universe:
                score = await self._calculate_stock_score(symbol)
                if score:
                    self.stock_scores[symbol] = score
                    
        except Exception as e:
            self.log_error(f"Failed to update stock scores: {e}")
    
    async def _publish_universe_update(self):
        """Publish universe update event"""
        try:
            await self.event_bus.publish(
                "STOCK_UNIVERSE_UPDATED",
                {
                    "universe": self.current_universe,
                    "scores": {symbol: score.score for symbol, score in self.stock_scores.items()},
                    "timestamp": datetime.now().isoformat(),
                    "selection_stats": self.selection_stats
                },
                source=self.name
            )
            
        except Exception as e:
            self.log_error(f"Failed to publish universe update: {e}")
    
    async def _handle_market_data(self, event):
        """Handle market data updates"""
        try:
            symbol = event.data.get('symbol')
            data = event.data.get('data')
            
            if symbol and data:
                # Update market data cache
                if symbol not in self.market_data:
                    self.market_data[symbol] = []
                
                # Store recent data point
                self.market_data[symbol] = data
                self.increment_message_count()
                
        except Exception as e:
            self.log_error(f"Failed to handle market data: {e}")
    
    async def _handle_universe_request(self, event):
        """Handle universe request from other agents"""
        try:
            requester = event.source
            
            # Send current universe
            await self.event_bus.publish(
                "STOCK_UNIVERSE_RESPONSE",
                {
                    "universe": self.current_universe,
                    "scores": {symbol: score.score for symbol, score in self.stock_scores.items()},
                    "requester": requester
                },
                source=self.name
            )
            
            self.log_info(f"Sent universe to {requester}: {len(self.current_universe)} stocks")
            
        except Exception as e:
            self.log_error(f"Failed to handle universe request: {e}")
    
    def get_current_universe(self) -> List[str]:
        """Get current stock universe"""
        return self.current_universe.copy()
    
    def get_stock_scores(self) -> Dict[str, StockScore]:
        """Get current stock scores"""
        return self.stock_scores.copy()
    
    def get_selection_stats(self) -> Dict[str, Any]:
        """Get selection statistics"""
        return self.selection_stats.copy()
    
    async def stop(self):
        """Stop the stock selector"""
        try:
            self.log_info("Stopping Dynamic Stock Selector...")
            
            self.running = False
            
            self.log_info("Dynamic Stock Selector stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping selector: {e}")